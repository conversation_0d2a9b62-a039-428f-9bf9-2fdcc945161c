<?php

// Include configuration file
require_once 'config.php';

/**
 * Notification system for anonymous messages
 * سیستم اطلاع‌رسانی برای پیام‌های ناشناس
 */

function bot($method, $datas = [])
{
    global $API_KEY;
    $url = "https://api.telegram.org/bot" . $API_KEY . "/" . $method;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $datas);
    $res = curl_exec($ch);
    if (curl_error($ch)) {
        var_dump(curl_error($ch));
    } else {
        return json_decode($res);
    }
}

function sendmessage($chat_id, $text, $reply_markup = null)
{
    $data = [
        'chat_id' => $chat_id,
        'text' => $text,
        'parse_mode' => 'HTML'
    ];

    if ($reply_markup) {
        $data['reply_markup'] = $reply_markup;
    }

    bot('sendMessage', $data);
}

/**
 * Send notification to user about new anonymous message
 * ارسال اطلاع‌رسانی به کاربر درباره پیام ناشناس جدید
 */
function notifyUserNewAnonymousMessage($target_username, $message_text) {
    $target_user_id = getUserIdByUsername($target_username);
    
    if (!$target_user_id) {
        return false; // User not found
    }
    
    // Check if user can receive anonymous messages
    if (!canReceiveAnonymousMessages($target_user_id)) {
        return false; // Privacy enabled
    }
    
    $notification_text = "💌 شما یک پیام ناشناس جدید دریافت کرده‌اید!\n\n";
    $notification_text .= "📝 پیش‌نمایش: " . mb_substr($message_text, 0, 50) . (mb_strlen($message_text) > 50 ? '...' : '') . "\n\n";
    $notification_text .= "برای مشاهده کامل پیام، از دکمه زیر استفاده کنید:";
    
    $keyboard = json_encode([
        'inline_keyboard' => [
            [
                ['text' => '💌 مشاهده پیام‌های ناشناس', 'callback_data' => 'view_anonymous_messages']
            ],
            [
                ['text' => '🔙 منو اصلی', 'callback_data' => 'back_to_menu']
            ]
        ]
    ]);
    
    sendmessage($target_user_id, $notification_text, $keyboard);
    return true;
}

/**
 * Check if notification should be sent
 * بررسی اینکه آیا اطلاع‌رسانی باید ارسال شود
 */
function shouldSendNotification($user_id) {
    // You can add logic here to check user notification preferences
    // For now, we'll send notifications to all users
    return true;
}

?>
