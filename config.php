<?php

// Bot Configuration
$API_KEY = '8101377108:AAEcJlRMUDQZvoJkruQruRRMHhaZLkEv1N4';

// JSON Database Configuration
$USER_DATA_FILE = 'user_data.json';
$USER_LANGUAGES_FILE = 'user_languages.json';
$USER_PRIVACY_FILE = 'user_privacy.json';

// Required channels for mandatory join
$required_channels = [
    '@speedxteam',
    '@speedx_bots'
];

/**
 * JSON Database Helper Functions
 */

// Read JSON file
function readJsonFile($filename) {
    if (file_exists($filename)) {
        $content = file_get_contents($filename);
        return json_decode($content, true) ?: [];
    }
    return [];
}

// Write JSON file
function writeJsonFile($filename, $data) {
    return file_put_contents($filename, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

// Get user privacy setting
function getUserPrivacy($user_id) {
    global $USER_PRIVACY_FILE;
    $privacy_data = readJsonFile($USER_PRIVACY_FILE);
    return $privacy_data[$user_id] ?? true; // Default: privacy enabled
}

// Set user privacy setting
function setUserPrivacy($user_id, $privacy_enabled) {
    global $USER_PRIVACY_FILE;
    $privacy_data = readJsonFile($USER_PRIVACY_FILE);
    $privacy_data[$user_id] = $privacy_enabled;
    return writeJsonFile($USER_PRIVACY_FILE, $privacy_data);
}

// Get user data
function getUserData($user_id) {
    global $USER_DATA_FILE;
    $user_data = readJsonFile($USER_DATA_FILE);
    return $user_data[$user_id] ?? [];
}

// Set user data
function setUserData($user_id, $data) {
    global $USER_DATA_FILE;
    $user_data = readJsonFile($USER_DATA_FILE);
    $user_data[$user_id] = $data;
    return writeJsonFile($USER_DATA_FILE, $user_data);
}

?>