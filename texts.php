<?php

/**
 * All bot texts and messages
 * تمامی متن‌ها و پیام‌های ربات
 */

class BotTexts {

    /**
     * Get user language from file storage
     * دریافت زبان کاربر از فایل
     */
    public static function getUserLanguage($user_id) {
        $lang_file = 'user_languages.json';
        if (file_exists($lang_file)) {
            $languages = json_decode(file_get_contents($lang_file), true);
            return $languages[$user_id] ?? 'fa';
        }
        return 'fa';
    }

    /**
     * Set user language in file storage
     * تنظیم زبان کاربر در فایل
     */
    public static function setUserLanguage($user_id, $lang) {
        $lang_file = 'user_languages.json';
        $languages = [];
        if (file_exists($lang_file)) {
            $languages = json_decode(file_get_contents($lang_file), true);
        }
        $languages[$user_id] = $lang;
        file_put_contents($lang_file, json_encode($languages));
    }

    /**
     * Welcome message text
     * متن پیام خوش آمدگویی
     */
    public static function getWelcomeText($first_name, $lang = 'fa') {
        if ($lang == 'en') {
            return "Hello $first_name 👋\n\n" .
                   "Welcome to Najvagram Bot.\n\n" .
                   "Using this bot, you can send anonymous messages in private chats and groups.\n\n" .
                   "<blockquote>📚 Please read the usage instructions through the help button or /help command.</blockquote>";
        }

        return "سلام $first_name 👋\n\n" .
               "به ربات نجوا گرام خوش آمدید.\n\n" .
               "با استفاده از این ربات شما می توانید متن و یا سایر محتوا را در چت های خصوصی و گروه نجوا کنید.\n\n" .
               "<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور /help مطالعه کنید.</blockquote>";
    }
    
    /**
     * Join channels message
     * پیام عضویت در کانال‌ها
     */
    public static function getJoinChannelsText($first_name) {
        return "سلام $first_name 👋\n\n" .
               "برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:";
    }
    
    /**
     * Not member message
     * پیام عدم عضویت
     */
    public static function getNotMemberText() {
        return "❌ شما هنوز عضو نیستید!\n\n" .
               "برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:";
    }
    
    /**
     * Help message
     * پیام راهنما
     */
    public static function getHelpText($lang = 'fa') {
        if ($lang == 'en') {
            return "📚 Najva Guide\n\n" .
                   "How to send anonymous messages:\n\n" .
                   "1. First, type the bot username in your chat ( @NajvaGram_Bot ) then add a space.\n" .
                   "2. As you can see, the najva sending options will appear.\n" .
                   "3. To send najva to someone, do as follows:\n" .
                   "<code>@NajvaGram_Bot TEXT ( message ) USERNAME ( person's username )</code>\n\n" .
                   "⚠️ Important notes:\n" .
                   "• Your messages are completely anonymous\n" .
                   "• No one can identify the sender\n" .
                   "• Avoid sending inappropriate content\n" .
                   "• This bot is designed for entertainment only";
        }

        return "📚 راهنمای نجوا\n\n" .
               "نحوه ارسال پیام ناشناس:\n\n" .
               "1. ابتدا در چت خود یوزرنیم ربات را تایپ کنید ( @NajvaGram_Bot ) سپس یک فاصله بدهید.\n" .
               "2. همانطور که مشاهده می کنید گزینه های ارسال نجوا نمایان میشوند.\n" .
               "3. برای ارسال نجوا به یک شخص اینگونه عمل کنید:\n" .
               "<code>@NajvaGram_Bot TEXT ( متن ) USERNAME ( یوزرنیم فرد )</code>\n\n" .
               "⚠️ نکات مهم:\n" .
               "• پیام‌های شما کاملاً ناشناس هستند\n" .
               "• هیچ‌کس نمی‌تواند فرستنده را شناسایی کند\n" .
               "• از ارسال محتوای نامناسب خودداری کنید\n" .
               "• این ربات صرفاً برای سرگرمی طراحی شده است";
    }
    

    /**
     * Support message
     * پیام پشتیبانی
     */
    public static function getSupportText($lang = 'fa') {
        if ($lang == 'en') {
            return "☎️ Support\n\n" .
                   "For support and assistance, please contact:\n\n" .
                   "📞 Support ID: @Alisaeghee\n\n" .
                   "Please describe your issue clearly so we can provide the best assistance.";
        }

        return "☎️ پشتیبانی\n\n" .
               "جهت پشتیبانی با آیدی زیر در ارتباط باشید:\n\n" .
               "📞 آیدی پشتیبانی: @Alisaeghee\n\n" .
               "لطفاً مشکل خود را به صورت واضح بیان کنید تا بتوانیم بهترین کمک را ارائه دهیم.";
    }
    
    /**
     * Language message
     * پیام زبان
     */
    public static function getLanguageText($lang = 'fa') {
        if ($lang == 'en') {
            return "🌐 Language Selection\n\n" .
                   "Please choose your preferred language:";
        }

        return "🌐 انتخاب زبان\n\n" .
               "لطفا زبان مورد نظر خود را انتخاب کنید:";
    }

    /**
     * Language changed confirmation
     * تایید تغییر زبان
     */
    public static function getLanguageChangedText($lang = 'fa') {
        if ($lang == 'en') {
            return "✅ Language changed to English successfully!";
        }

        return "✅ زبان با موفقیت به فارسی تغییر یافت!";
    }
    
    /**
     * Najva section message
     * پیام بخش نجوا
     */
    public static function getNajvaSectionText($lang = 'fa') {
        if ($lang == 'en') {
            return "💬 Anonymous Section\n\n" .
                   "Welcome to the anonymous messaging section. Here you can create anonymous messages and manage your settings.\n\n" .
                   "Choose an option below:";
        }

        return "💬 بخش نجوا\n\n" .
               "به بخش پیام‌رسانی ناشناس خوش آمدید. در اینجا می‌توانید پیام‌های ناشناس ایجاد کنید و تنظیمات خود را مدیریت کنید.\n\n" .
               "یکی از گزینه‌های زیر را انتخاب کنید:";
    }

    /**
     * Najva help text
     * متن راهنمای نجوا
     */
    public static function getNajvaHelpText($lang = 'fa') {
        if ($lang == 'en') {
            return "📚 Anonymous Messaging Guide\n\n" .
                   "<b>How to send anonymous messages:</b>\n\n" .
                   "1. First, type the bot username in your chat (@NajvaGram_Bot) then add a space.\n" .
                   "2. As you can see, the anonymous sending options will appear.\n" .
                   "3. To send an anonymous message to someone, do this:\n" .
                   "<code>@NajvaGram_Bot TEXT (message) USERNAME (person's username)</code>\n\n" .
                   "<b>⚠️ Important Notes:</b>\n" .
                   "• Your messages are completely anonymous\n" .
                   "• No one can identify the sender\n" .
                   "• Avoid sending inappropriate content\n" .
                   "• This bot is designed for entertainment purposes only";
        }

        return "📚 راهنمای نجوا\n\n" .
               "<b>نحوه ارسال پیام ناشناس:</b>\n\n" .
               "1. ابتدا در چت خود یوزرنیم ربات را تایپ کنید ( @NajvaGram_Bot ) سپس یک فاصله بدهید.\n" .
               "2. همانطور که مشاهده می کنید گزینه های ارسال نجوا نمایان میشوند.\n" .
               "3. برای ارسال نجوا به یک شخص اینگونه عمل کنید:\n" .
               "<code>@NajvaGram_Bot TEXT ( متن ) USERNAME ( یوزرنیم فرد )</code>\n\n" .
               "<b>⚠️ نکات مهم:</b>\n" .
               "• پیام‌های شما کاملاً ناشناس هستند\n" .
               "• هیچ‌کس نمی‌تواند فرستنده را شناسایی کند\n" .
               "• از ارسال محتوای نامناسب خودداری کنید\n" .
               "• این ربات صرفاً برای سرگرمی طراحی شده است";
    }

    /**
     * Privacy section text
     * متن بخش حریم خصوصی
     */
    public static function getPrivacyText($lang = 'fa', $privacy_enabled = true) {
        if ($lang == 'en') {
            $status = $privacy_enabled ? 'Enabled ✅' : 'Disabled ❌';
            return "👀 Privacy Settings\n\n" .
                   "Current Privacy Status: $status\n\n" .
                   "By enabling privacy, no one can send you anonymous messages.";
        }

        $status = $privacy_enabled ? 'فعال ✅' : 'غیرفعال ❌';
        return "👀 تنظیمات حریم خصوصی\n\n" .
               "وضعیت فعلی حریم خصوصی: $status\n\n" .
               "با فعال کردن حریم خصوصی هیچ فردی نمی تواند به شما نجوا ارسال کند.";
    }

    /**
     * Privacy toggle confirmation text
     * متن تایید تغییر حریم خصوصی
     */
    public static function getPrivacyToggleText($lang = 'fa', $privacy_enabled = true) {
        if ($lang == 'en') {
            if ($privacy_enabled) {
                return "✅ Privacy mode has been enabled!\n\nNo one can send you anonymous messages now.";
            } else {
                return "❌ Privacy mode has been disabled!\n\nOthers can now send you anonymous messages.";
            }
        }

        if ($privacy_enabled) {
            return "✅ حالت حریم خصوصی فعال شد!\n\nاکنون هیچ فردی نمی تواند به شما نجوا ارسال کند.";
        } else {
            return "❌ حالت حریم خصوصی غیرفعال شد!\n\nاکنون دیگران می توانند به شما نجوا ارسال کنند.";
        }
    }

    /**
     * Najva settings text
     * متن تنظیمات نجوا
     */
    public static function getNajvaSettingsText($lang = 'fa') {
        if ($lang == 'en') {
            return "⚙️ Anonymous Settings\n\n" .
                   "Configure your anonymous messaging preferences:\n\n" .
                   "<b>Current Settings:</b>\n" .
                   "• Language: " . ($lang == 'en' ? 'English 🇺🇸' : 'Persian 🇮🇷') . "\n" .
                   "• Anonymous Mode: Enabled ✅\n" .
                   "• Message Encryption: Active 🔒\n\n" .
                   "Choose an option below to modify your settings:";
        }

        return "⚙️ تنظیمات نجوا\n\n" .
               "تنظیمات پیام‌رسانی ناشناس خود را پیکربندی کنید:\n\n" .
               "<b>تنظیمات فعلی:</b>\n" .
               "• زبان: " . ($lang == 'en' ? 'English 🇺🇸' : 'فارسی 🇮🇷') . "\n" .
               "• حالت ناشناس: فعال ✅\n" .
               "• رمزنگاری پیام: فعال 🔒\n\n" .
               "برای تغییر تنظیمات، یکی از گزینه‌های زیر را انتخاب کنید:";
    }
    
    /**
     * Button texts
     * متن دکمه‌ها
     */
    public static function getButtons($lang = 'fa') {
        if ($lang == 'en') {
            return [
                'najva_section' => '💬 Anonymous Section',
                'privacy' => '👀 Privacy',
                'help' => '📚 Help',
                'support' => '☎️ Support',
                'language' => '🌐 Language',
                'check_membership' => '🔄 Check Membership',
                'back_to_menu' => '🔙 Back to Main Menu',
                'persian' => '🇮🇷 فارسی',
                'english' => '🇺🇸 English',

                'najva_help' => '📚 Guide',
                'najva_settings' => '⚙️ Settings',
                'change_language' => '🌐 Change Language',
                'enable_privacy' => '✅ Enable Privacy',
                'disable_privacy' => '❌ Disable Privacy'
            ];
        }

        return [
            'najva_section' => '💬 بخش نجوا',
            'privacy' => '👀 حریم خصوصی',
            'help' => '📚 راهنما',
            'support' => '☎️ پشتیبانی',
            'language' => '🌐 زبان',
            'check_membership' => '🔄 بررسی عضویت',
            'back_to_menu' => '🔙 بازگشت به منو اصلی',
            'persian' => '🇮🇷 فارسی',
            'english' => '🇺🇸 English',

            'najva_help' => '📚 راهنما',
            'najva_settings' => '⚙️ تنظیمات',
            'change_language' => '🌐 تغییر زبان',
            'enable_privacy' => '✅ فعال کردن حریم خصوصی',
            'disable_privacy' => '❌ غیرفعال کردن حریم خصوصی'
        ];
    }

    /**
     * Create main menu keyboard
     * ایجاد کیبورد منو اصلی
     */
    public static function createMainMenuKeyboard($lang = 'fa') {
        $buttons = self::getButtons($lang);

        return json_encode([
            'inline_keyboard' => [
                [
                    ['text' => $buttons['privacy'], 'callback_data' => 'privacy'],
                    ['text' => $buttons['najva_section'], 'callback_data' => 'najva_section']
                ],
                [
                    ['text' => $buttons['support'], 'callback_data' => 'support'],
                    ['text' => $buttons['help'], 'callback_data' => 'help']
                ],
                [
                    ['text' => $buttons['language'], 'callback_data' => 'language']
                ]
            ]
        ]);
    }

    /**
     * Create language selection keyboard
     * ایجاد کیبورد انتخاب زبان
     */
    public static function createLanguageKeyboard($lang = 'fa') {
        $buttons = self::getButtons($lang);

        return json_encode([
            'inline_keyboard' => [
                [
                    ['text' => $buttons['persian'], 'callback_data' => 'set_lang_fa'],
                    ['text' => $buttons['english'], 'callback_data' => 'set_lang_en']
                ],
                [
                    ['text' => $buttons['back_to_menu'], 'callback_data' => 'back_to_menu']
                ]
            ]
        ]);
    }

    /**
     * Create anonymous section keyboard
     * ایجاد کیبورد بخش نجوا
     */
    public static function createNajvaSectionKeyboard($lang = 'fa') {
        $buttons = self::getButtons($lang);

        return json_encode([
            'inline_keyboard' => [
                [
                    ['text' => $buttons['najva_help'], 'callback_data' => 'najva_help'],
                    ['text' => $buttons['najva_settings'], 'callback_data' => 'najva_settings']
                ],
                [
                    ['text' => $buttons['back_to_menu'], 'callback_data' => 'back_to_menu']
                ]
            ]
        ]);
    }

    /**
     * Create najva help keyboard
     * ایجاد کیبورد راهنمای نجوا
     */
    public static function createNajvaHelpKeyboard($lang = 'fa') {
        $buttons = self::getButtons($lang);

        return json_encode([
            'inline_keyboard' => [
                [
                    ['text' => $buttons['back_to_menu'], 'callback_data' => 'back_to_menu']
                ]
            ]
        ]);
    }

    /**
     * Create najva settings keyboard
     * ایجاد کیبورد تنظیمات نجوا
     */
    public static function createNajvaSettingsKeyboard($lang = 'fa') {
        $buttons = self::getButtons($lang);

        return json_encode([
            'inline_keyboard' => [
                [
                    ['text' => $buttons['change_language'], 'callback_data' => 'language']
                ],
                [
                    ['text' => $buttons['back_to_menu'], 'callback_data' => 'back_to_menu']
                ]
            ]
        ]);
    }

    /**
     * Create privacy settings keyboard
     * ایجاد کیبورد تنظیمات حریم خصوصی
     */
    public static function createPrivacyKeyboard($lang = 'fa', $privacy_enabled = true) {
        $buttons = self::getButtons($lang);

        if ($privacy_enabled) {
            $privacy_button = ['text' => $buttons['disable_privacy'], 'callback_data' => 'toggle_privacy_off'];
        } else {
            $privacy_button = ['text' => $buttons['enable_privacy'], 'callback_data' => 'toggle_privacy_on'];
        }

        return json_encode([
            'inline_keyboard' => [
                [
                    $privacy_button
                ],
                [
                    ['text' => $buttons['back_to_menu'], 'callback_data' => 'back_to_menu']
                ]
            ]
        ]);
    }

    /**
     * Create back to menu keyboard
     * ایجاد کیبورد بازگشت به منو
     */
    public static function createBackToMenuKeyboard($lang = 'fa') {
        $buttons = self::getButtons($lang);

        return json_encode([
            'inline_keyboard' => [
                [
                    ['text' => $buttons['back_to_menu'], 'callback_data' => 'back_to_menu']
                ]
            ]
        ]);
    }


}

?>
